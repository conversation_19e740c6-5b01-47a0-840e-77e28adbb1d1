import { ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatPhoneNumber(phone: string): string {
  // Format German phone numbers
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.startsWith('49')) {
    // International format
    return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)}${cleaned.slice(8, 11)}${cleaned.slice(11)}`
  } else if (cleaned.startsWith('0')) {
    // National format
    return `${cleaned.slice(0, 4)} - ${cleaned.slice(4, 8)}${cleaned.slice(8)}`
  }
  
  return phone
}

export function formatAddress(address: {
  street: string
  city: string
  postalCode: string
  country?: string
}): string {
  const { street, city, postalCode, country = 'Deutschland' } = address
  return `${street}, ${postalCode} ${city}, ${country}`
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength).replace(/\s+\S*$/, '') + '...'
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPhoneNumber(phone: string): boolean {
  // German phone number validation
  const phoneRegex = /^(\+49|0)[1-9]\d{1,14}$/
  return phoneRegex.test(phone.replace(/\s|-/g, ''))
}

export function scrollToElement(elementId: string, offset: number = 0): void {
  const element = document.getElementById(elementId)
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
    const offsetPosition = elementPosition - offset
    
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })
  }
}

export function getBusinessHours(): {
  isOpen: boolean
  nextOpenTime?: string
  todayHours?: string
} {
  const now = new Date()
  const day = now.getDay() // 0 = Sunday, 1 = Monday, etc.
  const hour = now.getHours()
  const minute = now.getMinutes()
  const currentTime = hour * 60 + minute
  
  // Business hours: Monday-Friday 10:00-18:30, Saturday 10:00-18:00
  const businessHours = {
    1: { open: 10 * 60, close: 18 * 60 + 30 }, // Monday
    2: { open: 10 * 60, close: 18 * 60 + 30 }, // Tuesday
    3: { open: 10 * 60, close: 18 * 60 + 30 }, // Wednesday
    4: { open: 10 * 60, close: 18 * 60 + 30 }, // Thursday
    5: { open: 10 * 60, close: 18 * 60 + 30 }, // Friday
    6: { open: 10 * 60, close: 18 * 60 }, // Saturday
  }
  
  const todaySchedule = businessHours[day as keyof typeof businessHours]
  
  if (!todaySchedule) {
    // Sunday - closed
    return {
      isOpen: false,
      nextOpenTime: 'Montag 10:00',
      todayHours: 'Geschlossen'
    }
  }
  
  const isOpen = currentTime >= todaySchedule.open && currentTime <= todaySchedule.close
  const openHour = Math.floor(todaySchedule.open / 60)
  const openMinute = todaySchedule.open % 60
  const closeHour = Math.floor(todaySchedule.close / 60)
  const closeMinute = todaySchedule.close % 60
  
  const todayHours = `${openHour.toString().padStart(2, '0')}:${openMinute.toString().padStart(2, '0')} - ${closeHour.toString().padStart(2, '0')}:${closeMinute.toString().padStart(2, '0')}`
  
  return {
    isOpen,
    todayHours,
    nextOpenTime: isOpen ? undefined : `${openHour.toString().padStart(2, '0')}:${openMinute.toString().padStart(2, '0')}`
  }
}

export function formatCurrency(amount: number, currency: string = 'EUR'): string {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export function formatDate(date: Date | string, format: 'short' | 'long' = 'short'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (format === 'long') {
    return new Intl.DateTimeFormat('de-DE', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(dateObj)
  }
  
  return new Intl.DateTimeFormat('de-DE').format(dateObj)
}

export function generateMetaDescription(content: string, maxLength: number = 160): string {
  const cleaned = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
  return truncateText(cleaned, maxLength)
}
