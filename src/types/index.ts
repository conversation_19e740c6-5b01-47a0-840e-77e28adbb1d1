export interface Location {
  id: string
  name: string
  address: {
    street: string
    city: string
    postalCode: string
    country: string
  }
  phone: string
  email?: string
  hours: {
    [key: string]: {
      open: string
      close: string
    }
  }
  coordinates?: {
    lat: number
    lng: number
  }
  services: string[]
}

export interface Service {
  id: string
  title: string
  description: string
  icon: string
  features: string[]
  benefits: string[]
  targetAudience: 'B2B' | 'B2C' | 'both'
  category: 'mobile' | 'internet' | 'security' | 'business'
  pricing?: {
    from: number
    currency: string
    period: string
  }
}

export interface Testimonial {
  id: string
  name: string
  role?: string
  company?: string
  content: string
  rating: number
  avatar?: string
  date: string
  location?: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  author: {
    name: string
    avatar?: string
    role?: string
  }
  publishedAt: string
  updatedAt?: string
  tags: string[]
  category: string
  featuredImage?: string
  readingTime: number
  seo: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
  }
}

export interface ContactForm {
  firstName: string
  lastName: string
  email: string
  phone: string
  message: string
  service?: string
  location?: string
  consent: boolean
}

export interface FAQ {
  id: string
  question: string
  answer: string
  category: string
  order: number
}

export interface Partner {
  id: string
  name: string
  logo: string
  description?: string
  website?: string
  category: 'network' | 'device' | 'service'
}

export interface NavigationItem {
  label: string
  href: string
  children?: NavigationItem[]
  icon?: string
  description?: string
}

export interface SocialLink {
  platform: string
  url: string
  icon: string
}

export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  canonical?: string
  openGraph?: {
    title?: string
    description?: string
    image?: string
    type?: string
  }
  twitter?: {
    card?: string
    title?: string
    description?: string
    image?: string
  }
  structuredData?: any
}

export interface AnimationConfig {
  duration: number
  delay?: number
  easing?: string
  stagger?: number
}

export interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends ComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  href?: string
  target?: string
  type?: 'button' | 'submit' | 'reset'
}

export interface CardProps extends ComponentProps {
  hover?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

export interface ModalProps extends ComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
}

export interface ToastProps {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export interface FormFieldProps {
  label?: string
  error?: string
  required?: boolean
  helpText?: string
  className?: string
}

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showFirstLast?: boolean
  showPrevNext?: boolean
  maxVisiblePages?: number
}

export interface SearchFilters {
  query?: string
  category?: string
  location?: string
  service?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface MediaQuery {
  sm: boolean
  md: boolean
  lg: boolean
  xl: boolean
  '2xl': boolean
}
