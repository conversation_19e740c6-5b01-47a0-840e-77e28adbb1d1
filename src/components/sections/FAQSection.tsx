'use client'

import React, { useRef, useState } from 'react'
import { motion, useInView, AnimatePresence } from 'framer-motion'
import { Plus, Minus, HelpCircle } from 'lucide-react'
import Card from '@/components/ui/Card'
import TextGenerateEffect from '@/components/ui/TextGenerateEffect'

const faqs = [
  {
    question: "Welche Mobilfunknetze bietet TMC an?",
    answer: "Wir sind Premium Partner von o2, Vodafone und Telekom und bieten Ihnen Zugang zu allen drei großen deutschen Mobilfunknetzen. So können Sie das für Sie beste Netz wählen."
  },
  {
    question: "Kann ich meine Rufnummer zu TMC mitnehmen?",
    answer: "Ja, die Rufnummernmitnahme ist bei uns kostenlos möglich. Wir übernehmen den kompletten Wechselprozess für Sie und sorgen dafür, dass Sie durchgehend erreichbar bleiben."
  },
  {
    question: "Welche Internetgeschwindigkeiten sind verfügbar?",
    answer: "Je nach Standort bieten wir DSL bis 250 Mbit/s, Kabel bis 1000 Mbit/s und Glasfaser bis 1000 Mbit/s. Gerne prüfen wir die Verfügbarkeit an Ihrer Adresse."
  },
  {
    question: "Bietet TMC auch Geschäftslösungen an?",
    answer: "Ja, wir haben spezielle Tarife und Lösungen für Unternehmen. Von einzelnen Geschäftshandys bis hin zu kompletten Kommunikationslösungen für größere Firmen."
  },
  {
    question: "Wie lange dauert die Aktivierung eines neuen Vertrags?",
    answer: "Mobilfunkverträge werden meist innerhalb von 24 Stunden aktiviert. Bei Festnetz und Internet beträgt die Schaltzeit in der Regel 1-2 Wochen, abhängig vom gewählten Anbieter."
  },
  {
    question: "Gibt es eine Mindestvertragslaufzeit?",
    answer: "Das hängt vom gewählten Tarif ab. Wir bieten sowohl Verträge mit 24 Monaten Laufzeit als auch flexible Tarife ohne Mindestlaufzeit oder mit nur einem Monat Kündigungsfrist."
  },
  {
    question: "Was passiert bei technischen Problemen?",
    answer: "Unser Support-Team ist für Sie da. Bei Mobilfunk-Problemen helfen wir sofort vor Ort, bei Internet-Störungen koordinieren wir mit dem jeweiligen Anbieter und halten Sie über den Reparaturstatus auf dem Laufenden."
  },
  {
    question: "Kann ich meinen Vertrag in den Geschäftsstellen verwalten?",
    answer: "Ja, Sie können jederzeit in unsere Geschäftsstellen in Gießen oder Limburg kommen. Wir helfen bei Vertragsänderungen, Tarifwechseln oder anderen Anliegen persönlich vor Ort."
  },
  {
    question: "Bietet TMC auch Handyversicherungen an?",
    answer: "Ja, wir bieten umfassende Versicherungslösungen für Smartphones und andere Geräte. Diese decken Diebstahl, Beschädigung und Defekte ab."
  },
  {
    question: "Gibt es Familientarife oder Rabatte für mehrere Verträge?",
    answer: "Ja, wir bieten attraktive Familientarife und Mengenrabatte. Je mehr Verträge Sie bei uns haben, desto günstiger wird es. Lassen Sie sich gerne beraten."
  }
]

const FAQSection: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section ref={ref} className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-4">
            <HelpCircle className="w-4 h-4 mr-2" />
            Häufige Fragen
          </span>
          
          <TextGenerateEffect
            words="Antworten auf Ihre Fragen"
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-6"
          />
          
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            Hier finden Sie Antworten auf die häufigsten Fragen zu unseren Services. 
            Sollten Sie weitere Fragen haben, kontaktieren Sie uns gerne direkt.
          </p>
        </motion.div>

        {/* FAQ List */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              >
                <Card className="overflow-hidden border-black/10 hover:border-black/20 transition-colors">
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-neutral-50 transition-colors"
                  >
                    <h3 className="text-lg font-semibold text-black pr-4">
                      {faq.question}
                    </h3>
                    <div className="flex-shrink-0">
                      {openIndex === index ? (
                        <Minus className="w-5 h-5 text-black" />
                      ) : (
                        <Plus className="w-5 h-5 text-black" />
                      )}
                    </div>
                  </button>
                  
                  <AnimatePresence>
                    {openIndex === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-6">
                          <div className="border-t border-neutral-200 pt-4">
                            <p className="text-neutral-600 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Contact CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <Card className="p-8 md:p-12 bg-neutral-50 border-black/10">
            <h3 className="text-2xl md:text-3xl font-bold text-black mb-4">
              Ihre Frage war nicht dabei?
            </h3>
            <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
              Kein Problem! Unser Team beantwortet gerne alle Ihre Fragen persönlich. 
              Kontaktieren Sie uns telefonisch oder besuchen Sie uns vor Ort.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="tel:+*************"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 bg-black text-white font-medium rounded-lg hover:bg-neutral-800 transition-colors"
              >
                Jetzt anrufen
              </motion.a>
              <motion.a
                href="/kontakt"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 border border-black text-black font-medium rounded-lg hover:bg-black hover:text-white transition-colors"
              >
                Kontakt aufnehmen
              </motion.a>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default FAQSection
