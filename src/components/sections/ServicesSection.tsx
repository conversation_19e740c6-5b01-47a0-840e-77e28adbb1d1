'use client'

import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { 
  Smartphone, 
  Wifi, 
  Shield, 
  Building2, 
  ArrowRight,
  Check,
  Star
} from 'lucide-react'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'

const services = [
  {
    id: 'mobilfunk',
    title: 'Mobilfunk',
    description: 'Entdecken Sie unsere attraktiven Mobilfunktarife, die auf Ihre individuellen Bedürfnisse zugeschnitten sind.',
    icon: Smartphone,
    features: [
      'Flexible Laufzeiten',
      'Hohe Datenvolumen',
      'Neueste Smartphones',
      'Günstige Preise'
    ],
    benefits: [
      'Persönliche Beratung vor Ort',
      'Alle Netze verfügbar',
      'Rufnummernmitnahme',
      'Schnelle Aktivierung'
    ],
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600'
  },
  {
    id: 'internet',
    title: '<PERSON><PERSON>, <PERSON><PERSON> und Glasfaser',
    description: 'Erleben Sie blitzschnelles Internet mit unseren DSL-, Kabel- und Glasfaserangeboten.',
    icon: Wifi,
    features: [
      'Bis zu 1000 Mbit/s',
      'Stabile Verbindungen',
      'Alle Technologien',
      'Faire Preise'
    ],
    benefits: [
      'Kostenlose Installation',
      'WLAN-Router inklusive',
      '24/7 Support',
      'Keine Mindestlaufzeit'
    ],
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600'
  },
  {
    id: 'business',
    title: 'Geschäftskundenkompetenz',
    description: 'Maßgeschneiderte Lösungen für Unternehmen mit erstklassigem Service und individueller Beratung.',
    icon: Building2,
    features: [
      'Individuelle Lösungen',
      'Dedicated Support',
      'Skalierbare Tarife',
      'Kostenoptimierung'
    ],
    benefits: [
      'Persönlicher Ansprechpartner',
      'Flexible Vertragsgestaltung',
      'Schnelle Umsetzung',
      'Regelmäßige Optimierung'
    ],
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600'
  },
  {
    id: 'security',
    title: 'Sicherheit und Handyversicherung',
    description: 'Schützen Sie Ihr Smartphone und Ihre Daten mit unseren umfassenden Sicherheitslösungen.',
    icon: Shield,
    features: [
      'Diebstahlschutz',
      'Schadensabdeckung',
      'Datenschutz',
      'Schnelle Abwicklung'
    ],
    benefits: [
      'Sofortiger Ersatz',
      'Weltweiter Schutz',
      'Einfache Abwicklung',
      'Faire Konditionen'
    ],
    color: 'from-red-500 to-red-600',
    bgColor: 'bg-red-50',
    iconColor: 'text-red-600'
  }
]

const ServicesSection: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  }

  return (
    <section ref={ref} className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-4">
            <Star className="w-4 h-4 mr-2" />
            Unsere Leistungen
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
            Alles aus einer Hand für Ihre{' '}
            <span className="text-gradient-primary">Kommunikation</span>
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            Von Mobilfunk über Internet bis hin zu Geschäftslösungen – 
            wir bieten Ihnen das komplette Spektrum moderner Kommunikationstechnologie.
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              variants={itemVariants}
              whileHover={{ y: -8 }}
              className="group"
            >
              <Card 
                className="h-full p-0 overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500"
              >
                {/* Card Header */}
                <div className={`${service.bgColor} p-6 relative overflow-hidden`}>
                  <div className="relative z-10">
                    <div className={`w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <service.icon className={`w-8 h-8 ${service.iconColor}`} />
                    </div>
                    <h3 className="text-xl font-bold text-neutral-900 mb-2">
                      {service.title}
                    </h3>
                    <p className="text-neutral-700 text-sm leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                  
                  {/* Background Pattern */}
                  <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                    <div className="w-full h-full bg-gradient-to-br from-neutral-900 to-transparent rounded-full transform translate-x-8 -translate-y-8" />
                  </div>
                </div>

                {/* Card Content */}
                <div className="p-6">
                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-neutral-900 mb-3 uppercase tracking-wide">
                      Features
                    </h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-sm text-neutral-600">
                          <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Benefits */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-neutral-900 mb-3 uppercase tracking-wide">
                      Ihre Vorteile
                    </h4>
                    <ul className="space-y-2">
                      {service.benefits.slice(0, 2).map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-sm text-neutral-600">
                          <div className="w-2 h-2 bg-primary-500 rounded-full mr-3 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full group-hover:bg-primary-600 group-hover:text-white group-hover:border-primary-600 transition-all duration-300"
                  >
                    Mehr erfahren
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Nicht sicher, welche Lösung die richtige für Sie ist?
            </h3>
            <p className="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Lassen Sie sich von unseren Experten beraten. Wir finden gemeinsam 
              die perfekte Lösung für Ihre Bedürfnisse.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="secondary"
                size="lg"
                href="/kontakt"
                className="bg-white text-primary-900 hover:bg-white/90"
              >
                Kostenlose Beratung
              </Button>
              <Button 
                variant="outline"
                size="lg"
                href="/standorte"
                className="border-white text-white hover:bg-white hover:text-primary-900"
              >
                Standorte finden
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default ServicesSection
