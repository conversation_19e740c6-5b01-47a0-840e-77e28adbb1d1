'use client'

import React, { useRef } from 'react'
import dynamic from 'next/dynamic'
import { motion, useScroll, useTransform } from 'framer-motion'
import { ArrowRight, Play, Star, Users, Award, Zap, Globe as GlobeIcon, Smartphone, Wifi } from 'lucide-react'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import { GlobeConfig } from '@/components/ui/Globe'
import BackgroundBeams from '@/components/ui/BackgroundBeams'
import Spotlight from '@/components/ui/Spotlight'
import TextGenerateEffect from '@/components/ui/TextGenerateEffect'

// Dynamically import the Globe component to avoid SSR issues
const World = dynamic(() => import('@/components/ui/Globe').then(mod => ({ default: mod.World })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="w-64 h-64 bg-neutral-100 rounded-full animate-pulse flex items-center justify-center">
        <GlobeIcon className="w-16 h-16 text-neutral-400" />
      </div>
    </div>
  )
})

const stats = [
  {
    icon: Users,
    value: '20+',
    label: 'Jahre Erfahrung',
    description: 'Seit 2003 Ihr vertrauensvoller Partner'
  },
  {
    icon: Award,
    value: '3',
    label: 'Premium Partner',
    description: 'o2, Vodafone & Telekom'
  },
  {
    icon: Star,
    value: '4.8',
    label: 'Kundenbewertung',
    description: 'Basierend auf Google Rezensionen'
  },
  {
    icon: Zap,
    value: '4',
    label: 'Standorte',
    description: 'In Gießen und Limburg'
  }
]

const globeConfig: GlobeConfig = {
  pointSize: 4,
  globeColor: "#062056",
  showAtmosphere: true,
  atmosphereColor: "#FFFFFF",
  atmosphereAltitude: 0.1,
  emissive: "#062056",
  emissiveIntensity: 0.1,
  shininess: 0.9,
  polygonColor: "rgba(255,255,255,0.7)",
  ambientLight: "#38bdf8",
  directionalLeftLight: "#ffffff",
  directionalTopLight: "#ffffff",
  pointLight: "#ffffff",
  arcTime: 1000,
  arcLength: 0.9,
  rings: 1,
  maxRings: 3,
  initialPosition: { lat: 50.5841, lng: 8.6794 },
  autoRotate: true,
  autoRotateSpeed: 0.5,
};

const colors = ["#06b6d4", "#3b82f6", "#6366f1"];

const globeData = [
  {
    order: 1,
    startLat: 50.5841, // Gießen
    startLng: 8.6794,
    endLat: 50.3836, // Limburg
    endLng: 8.0503,
    arcAlt: 0.1,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 2,
    startLat: 50.5841, // Gießen
    startLng: 8.6794,
    endLat: 52.5200, // Berlin
    endLng: 13.4050,
    arcAlt: 0.2,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 3,
    startLat: 50.3836, // Limburg
    startLng: 8.0503,
    endLat: 48.1351, // Munich
    endLng: 11.5820,
    arcAlt: 0.15,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 4,
    startLat: 50.5841, // Gießen
    startLng: 8.6794,
    endLat: 51.5072, // London
    endLng: -0.1276,
    arcAlt: 0.3,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 5,
    startLat: 50.5841, // Gießen
    startLng: 8.6794,
    endLat: 48.8566, // Paris
    endLng: 2.3522,
    arcAlt: 0.2,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 6,
    startLat: 50.3836, // Limburg
    startLng: 8.0503,
    endLat: 52.3676, // Amsterdam
    endLng: 4.9041,
    arcAlt: 0.2,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 7,
    startLat: 50.5841, // Gießen
    startLng: 8.6794,
    endLat: 41.9028, // Rome
    endLng: 12.4964,
    arcAlt: 0.3,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
  {
    order: 8,
    startLat: 50.3836, // Limburg
    startLng: 8.0503,
    endLat: 40.7128, // New York
    endLng: -74.006,
    arcAlt: 0.5,
    color: colors[Math.floor(Math.random() * (colors.length - 1))],
  },
];

const HeroSection: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start']
  })

  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%'])
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0])

  return (
    <section ref={containerRef} className="relative min-h-screen flex items-center overflow-hidden bg-white">
      {/* Background with Parallax */}
      <motion.div
        style={{ y }}
        className="absolute inset-0 z-0"
      >
        <div className="absolute inset-0 bg-white" />
        <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 to-white" />
        <BackgroundBeams className="opacity-30" />
        <Spotlight className="opacity-50" fill="rgba(0, 0, 0, 0.05)" />
        
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          {/* Floating Orbs */}
          <motion.div
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-1/4 left-1/4 w-64 h-64 bg-black/5 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              x: [0, -80, 0],
              y: [0, 60, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-black/3 rounded-full blur-3xl"
          />

          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="h-full w-full bg-[linear-gradient(rgba(0,0,0,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.1)_1px,transparent_1px)] bg-[size:50px_50px]" />
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div 
        style={{ opacity }}
        className="relative z-10 container-custom"
      >
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="text-black order-2 lg:order-1">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-6"
            >
              <span className="inline-flex items-center px-4 py-2 rounded-full bg-black/5 backdrop-blur-sm text-sm font-medium mb-6 border border-black/10">
                <Star className="w-4 h-4 mr-2 text-black" />
                Seit 2003 Ihr vertrauensvoller Partner
              </span>
            </motion.div>

            <TextGenerateEffect
              words="Ihr Partner für moderne Kommunikation"
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-4 sm:mb-6"
            />

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-lg sm:text-xl text-neutral-600 mb-6 sm:mb-8 leading-relaxed"
            >
              Als qualifizierter Partner von o2, Vodafone und Telekom bieten wir Ihnen
              maßgeschneiderte Lösungen für Mobilfunk, Internet und Geschäftskommunikation.
              Vertrauen Sie auf über 20 Jahre Erfahrung und persönliche Beratung.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-8 sm:mb-12"
            >
              <Button
                variant="primary"
                size="lg"
                href="/kontakt"
                className="bg-black text-white hover:bg-neutral-800"
              >
                Kostenlose Beratung
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-black text-black hover:bg-black hover:text-white"
              >
                <Play className="w-5 h-5 mr-2" />
                Mehr erfahren
              </Button>
            </motion.div>

            {/* Stats Grid */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="flex items-center justify-center w-12 h-12 bg-black/10 backdrop-blur-sm rounded-lg mb-3 mx-auto border border-black/20">
                    <stat.icon className="w-6 h-6 text-black" />
                  </div>
                  <div className="text-2xl font-bold text-black mb-1">{stat.value}</div>
                  <div className="text-sm text-neutral-700 font-medium">{stat.label}</div>
                  <div className="text-xs text-neutral-500 mt-1">{stat.description}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Right Column - Globe */}
          <div className="relative order-1 lg:order-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
              className="relative h-[400px] sm:h-[500px] lg:h-[600px] overflow-hidden"
            >
              {/* Globe Component with GitHub styling */}
              <div className="absolute w-full h-full">
                <div className="absolute w-full bottom-0 inset-x-0 h-40 bg-gradient-to-b pointer-events-none select-none from-transparent to-white z-40" />
                <div className="absolute w-full -bottom-20 h-72 md:h-full z-10">
                  <World
                    globeConfig={globeConfig}
                    data={globeData}
                  />
                </div>
              </div>

              {/* Overlay Info */}
              <div className="absolute top-4 left-4 z-50">
                <Card className="bg-white/90 backdrop-blur-md border-black/10 p-4 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center">
                      <GlobeIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-black">Deutschlandweit</div>
                      <div className="text-xs text-neutral-600">Vertrauensvoller Service</div>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Floating Service Cards */}
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute top-20 -right-4 z-50"
              >
                <Card className="bg-white/90 backdrop-blur-md border-black/10 p-4 shadow-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center">
                      <Smartphone className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-black">Mobilfunk</div>
                      <div className="text-xs text-neutral-600">Alle Netze</div>
                    </div>
                  </div>
                </Card>
              </motion.div>

              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                className="absolute bottom-20 -left-4 z-50"
              >
                <Card className="bg-black text-white p-4 shadow-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <Wifi className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold">Internet</div>
                      <div className="text-xs text-white/80">Bis 1000 Mbit/s</div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-black/30 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-black/50 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  )
}

export default HeroSection
