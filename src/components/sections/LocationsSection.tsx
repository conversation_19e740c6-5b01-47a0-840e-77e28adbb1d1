'use client'

import React, { useRef, useState } from 'react'
import { motion, useInView } from 'framer-motion'
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Navigation,
  Car,
  Train,
  ExternalLink
} from 'lucide-react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const locations = [
  {
    id: 'o2-giessen',
    name: 'o2 Shop Gießen',
    address: {
      street: 'Neustadt 8',
      city: 'Gießen',
      postalCode: '35390',
      country: 'Deutschland'
    },
    coordinates: { lat: 50.5841, lng: 8.6794 },
    phone: '+49 641 98389836',
    email: 'g<PERSON><EMAIL>',
    hours: {
      'Mo-Fr': '10:00 - 18:30',
      'Sa': '10:00 - 18:00',
      'So': 'Geschlossen'
    },
    services: ['o2 Mobilfunk', 'Smartphones', 'Tarife', 'Beratung'],
    parking: 'Kostenlose Parkplätze verfügbar',
    publicTransport: 'Bushaltestelle Neustadt (2 Min. Fußweg)',
    description: 'Offizieller o2 Shop mit dem kompletten o2 Sortiment und persönlicher Beratung.'
  },
  {
    id: 'telemediacenter-giessen',
    name: 'Telemediacenter Gießen',
    address: {
      street: 'Neustadt 6',
      city: 'Gießen',
      postalCode: '35390',
      country: 'Deutschland'
    },
    coordinates: { lat: 50.5841, lng: 8.6792 },
    phone: '+49 641 92369999',
    email: '<EMAIL>',
    hours: {
      'Mo-Fr': '10:00 - 18:30',
      'Sa': '10:00 - 18:00',
      'So': 'Geschlossen'
    },
    services: ['Alle Netze', 'Internet', 'Geschäftskunden', 'Reparaturen'],
    parking: 'Kostenlose Parkplätze verfügbar',
    publicTransport: 'Bushaltestelle Neustadt (2 Min. Fußweg)',
    description: 'Telemediacenter mit allen Mobilfunknetzen und umfassendem Service.'
  },
  {
    id: 'spartarif24-giessen',
    name: 'Spartarif24 Gießen',
    address: {
      street: 'Bahnhofstraße 12',
      city: 'Gießen',
      postalCode: '35390',
      country: 'Deutschland'
    },
    coordinates: { lat: 50.5847, lng: 8.6785 },
    phone: '+49 641 97272296',
    email: '<EMAIL>',
    hours: {
      'Mo-Fr': '10:00 - 18:30',
      'Sa': '10:00 - 18:00',
      'So': 'Geschlossen'
    },
    services: ['Günstige Tarife', 'Mobilfunk', 'Beratung', 'Vergleich'],
    parking: 'Bahnhofsparkhaus (100m entfernt)',
    publicTransport: 'Hauptbahnhof Gießen (3 Min. Fußweg)',
    description: 'Spezialist für günstige Mobilfunktarife mit bestem Preis-Leistungs-Verhältnis.'
  },
  {
    id: 'telemediacenter-limburg',
    name: 'Telemediacenter Limburg',
    address: {
      street: 'Bahnhofstraße 4',
      city: 'Limburg an der Lahn',
      postalCode: '65549',
      country: 'Deutschland'
    },
    coordinates: { lat: 50.3836, lng: 8.0503 },
    phone: '+49 6431 590562',
    email: '<EMAIL>',
    hours: {
      'Mo-Fr': '10:30 - 19:00',
      'Sa': '10:30 - 17:00',
      'So': 'Geschlossen'
    },
    services: ['Alle Netze', 'Internet', 'Geschäftskunden', 'Beratung'],
    parking: 'Parkhaus Bahnhof (50m entfernt)',
    publicTransport: 'Bahnhof Limburg (1 Min. Fußweg)',
    description: 'Zentral gelegen am Bahnhof Limburg mit allen Mobilfunknetzen und Services.'
  }
]

const LocationsSection: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })
  const [selectedLocation, setSelectedLocation] = useState(locations[0])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  }

  const getDirectionsUrl = (location: typeof locations[0]) => {
    const address = `${location.address.street}, ${location.address.postalCode} ${location.address.city}`
    return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(address)}`
  }

  return (
    <section ref={ref} className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-4">
            <MapPin className="w-4 h-4 mr-2" />
            Unsere Standorte
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-6">
            Besuchen Sie uns{' '}
            <span className="bg-gradient-to-r from-black to-neutral-600 bg-clip-text text-transparent">
              vor Ort
            </span>
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            Persönliche Beratung in unseren modernen Geschäftsstellen in Gießen und Limburg. 
            Unser Team freut sich auf Ihren Besuch.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Location Cards */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="space-y-6"
          >
            {locations.map((location, index) => (
              <motion.div
                key={location.id}
                variants={itemVariants}
                onClick={() => setSelectedLocation(location)}
                className="cursor-pointer"
              >
                <Card 
                  className={`p-6 transition-all duration-300 hover:shadow-lg ${
                    selectedLocation.id === location.id 
                      ? 'border-black shadow-lg' 
                      : 'border-black/10 hover:border-black/20'
                  }`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-black mb-2">{location.name}</h3>
                      <div className="flex items-start space-x-2 text-neutral-600">
                        <MapPin className="w-4 h-4 mt-1 flex-shrink-0" />
                        <div>
                          <div>{location.address.street}</div>
                          <div>{location.address.postalCode} {location.address.city}</div>
                        </div>
                      </div>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      selectedLocation.id === location.id ? 'bg-black' : 'bg-neutral-300'
                    }`} />
                  </div>

                  <p className="text-neutral-600 mb-4">{location.description}</p>

                  {/* Contact Info */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center space-x-2 text-sm">
                      <Phone className="w-4 h-4 text-black" />
                      <a href={`tel:${location.phone}`} className="hover:text-black transition-colors">
                        {location.phone}
                      </a>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <Mail className="w-4 h-4 text-black" />
                      <a href={`mailto:${location.email}`} className="hover:text-black transition-colors">
                        {location.email}
                      </a>
                    </div>
                  </div>

                  {/* Opening Hours */}
                  <div className="mb-4">
                    <div className="flex items-center space-x-2 text-sm font-medium text-black mb-2">
                      <Clock className="w-4 h-4" />
                      <span>Öffnungszeiten</span>
                    </div>
                    <div className="text-sm text-neutral-600 space-y-1">
                      {Object.entries(location.hours).map(([day, hours]) => (
                        <div key={day} className="flex justify-between">
                          <span>{day}:</span>
                          <span>{hours}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Services */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-black mb-2">Services</div>
                    <div className="flex flex-wrap gap-2">
                      {location.services.map((service) => (
                        <span
                          key={service}
                          className="px-2 py-1 bg-black/5 text-black text-xs rounded-full"
                        >
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Transport Info */}
                  <div className="space-y-2 text-xs text-neutral-500">
                    <div className="flex items-center space-x-2">
                      <Car className="w-3 h-3" />
                      <span>{location.parking}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Train className="w-3 h-3" />
                      <span>{location.publicTransport}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      href={getDirectionsUrl(location)}
                      target="_blank"
                      className="flex-1 border-black text-black hover:bg-black hover:text-white"
                    >
                      <Navigation className="w-4 h-4 mr-2" />
                      Route planen
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      href={`tel:${location.phone}`}
                      className="flex-1 bg-black text-white hover:bg-neutral-800"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Anrufen
                    </Button>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <Card className="h-[600px] overflow-hidden border-black/10">
              {/* Map Placeholder */}
              <div className="w-full h-full bg-gradient-to-br from-neutral-100 to-neutral-200 flex items-center justify-center relative">
                <div className="text-center">
                  <MapPin className="w-16 h-16 text-black mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-black mb-2">
                    {selectedLocation.name}
                  </h3>
                  <p className="text-neutral-600 mb-4">
                    {selectedLocation.address.street}<br />
                    {selectedLocation.address.postalCode} {selectedLocation.address.city}
                  </p>
                  <Button
                    variant="primary"
                    href={getDirectionsUrl(selectedLocation)}
                    target="_blank"
                    className="bg-black text-white hover:bg-neutral-800"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    In Google Maps öffnen
                  </Button>
                </div>
                
                {/* Location Markers */}
                {locations.map((location, index) => (
                  <div
                    key={location.id}
                    className={`absolute w-4 h-4 rounded-full border-2 border-white shadow-lg cursor-pointer transition-all duration-300 ${
                      selectedLocation.id === location.id 
                        ? 'bg-black scale-150' 
                        : 'bg-neutral-400 hover:bg-black'
                    }`}
                    style={{
                      left: `${30 + index * 40}%`,
                      top: `${40 + index * 20}%`
                    }}
                    onClick={() => setSelectedLocation(location)}
                  />
                ))}
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default LocationsSection
