'use client'

import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Star, Quote } from 'lucide-react'
import InfiniteMovingCards from '@/components/ui/InfiniteMovingCards'
import TextGenerateEffect from '@/components/ui/TextGenerateEffect'

const testimonials = [
  {
    quote: "Seit Jahren bin ich Kunde bei TMC und bin immer wieder begeistert von der kompetenten Beratung. Das Team nimmt sich Zeit für individuelle Lösungen und der Service ist einfach top!",
    name: "<PERSON>",
    title: "Geschäftsführer, Weber <PERSON>"
  },
  {
    quote: "Die Mitarbeiter von TMC haben mir geholfen, den perfekten Tarif zu finden. Sehr freundlich, professionell und immer erreichbar. Kann ich nur weiterempfehlen!",
    name: "<PERSON>",
    title: "Privatkunde"
  },
  {
    quote: "Als Unternehmen sind wir auf zuverlässige Kommunikation angewiesen. TMC hat uns eine maßgeschneiderte Lösung angeboten, die perfekt zu unseren Bedürfnissen passt.",
    name: "<PERSON>",
    title: "IT-Leiter, Klein & Partner"
  },
  {
    quote: "Schnelle Hilfe bei Problemen, faire Preise und ehrliche Beratung - so stelle ich mir Kundenservice vor. TMC ist mein Telekommunikationspartner des Vertrauens.",
    name: "Anna Schmidt",
    title: "Privatkunde"
  },
  {
    quote: "Die Geschäftsstelle in Gießen ist modern und das Personal sehr kompetent. Ich wurde umfassend beraten und habe genau das bekommen, was ich gesucht habe.",
    name: "Robert Fischer",
    title: "Privatkunde"
  },
  {
    quote: "TMC hat uns bei der Digitalisierung unseres Unternehmens hervorragend unterstützt. Von der Planung bis zur Umsetzung - alles aus einer Hand und professionell umgesetzt.",
    name: "Julia Hoffmann",
    title: "Geschäftsführerin, Hoffmann Solutions"
  }
]

const stats = [
  { value: "4.8", label: "Google Bewertung", icon: Star },
  { value: "1000+", label: "Zufriedene Kunden", icon: Quote },
  { value: "98%", label: "Weiterempfehlung", icon: Star },
  { value: "20+", label: "Jahre Erfahrung", icon: Quote }
]

const TestimonialsSection: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })

  return (
    <section ref={ref} className="section-padding bg-neutral-50 overflow-hidden">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-4">
            <Star className="w-4 h-4 mr-2" />
            Kundenstimmen
          </span>
          
          <TextGenerateEffect
            words="Was unsere Kunden über uns sagen"
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-6"
          />
          
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            Über 1000 zufriedene Kunden vertrauen auf unsere Expertise und unseren Service. 
            Lesen Sie, was sie über ihre Erfahrungen mit der TMC Gruppe sagen.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
              className="text-center"
            >
              <div className="flex items-center justify-center w-16 h-16 bg-black rounded-full mx-auto mb-4">
                <stat.icon className="w-8 h-8 text-white" />
              </div>
              <div className="text-3xl lg:text-4xl font-bold text-black mb-2">
                {stat.value}
              </div>
              <div className="text-sm font-medium text-neutral-600">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Testimonials Carousel */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="relative"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-black mb-8 text-center">
            Echte Bewertungen von echten Kunden
          </h3>
          
          <InfiniteMovingCards
            items={testimonials}
            direction="right"
            speed="slow"
            pauseOnHover={true}
            className="mb-8"
          />
          
          <InfiniteMovingCards
            items={testimonials.slice().reverse()}
            direction="left"
            speed="slow"
            pauseOnHover={true}
          />
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="text-center mt-16"
        >
          <div className="bg-black text-white rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Werden Sie unser nächster zufriedener Kunde
            </h3>
            <p className="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Erleben Sie selbst, warum über 1000 Kunden der TMC Gruppe vertrauen. 
              Lassen Sie sich kostenlos und unverbindlich beraten.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/kontakt"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 bg-white text-black font-medium rounded-lg hover:bg-neutral-100 transition-colors"
              >
                Kostenlose Beratung
              </motion.a>
              <motion.a
                href="tel:+4964198389836"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 border border-white text-white font-medium rounded-lg hover:bg-white hover:text-black transition-colors"
              >
                Jetzt anrufen
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default TestimonialsSection
