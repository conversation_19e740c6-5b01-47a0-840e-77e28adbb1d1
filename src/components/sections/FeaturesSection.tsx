'use client'

import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { 
  Smartphone, 
  Wifi, 
  Shield, 
  Building2, 
  Clock, 
  Users, 
  Award,
  Headphones,
  MapPin,
  Zap
} from 'lucide-react'
import { BentoGrid, BentoGridItem } from '@/components/ui/BentoGrid'
import TextGenerateEffect from '@/components/ui/TextGenerateEffect'

const features = [
  {
    title: "Alle Netze verfügbar",
    description: "o2, Vodafone und Telekom - wir bieten Ihnen Zugang zu allen großen Mobilfunknetzen in Deutschland.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="grid grid-cols-3 gap-4">
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <span className="text-white text-xs font-bold">o2</span>
            </div>
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <span className="text-white text-xs font-bold">VF</span>
            </div>
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <span className="text-white text-xs font-bold">T</span>
            </div>
          </div>
        </div>
      </div>
    ),
    icon: <Smartphone className="h-4 w-4 text-black" />,
    className: "md:col-span-2",
  },
  {
    title: "24/7 Support",
    description: "Unser Kundenservice ist rund um die Uhr für Sie da.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Clock className="w-12 h-12 text-black/60" />
        </div>
      </div>
    ),
    icon: <Headphones className="h-4 w-4 text-black" />,
  },
  {
    title: "Persönliche Beratung",
    description: "Individuelle Lösungen für Ihre Bedürfnisse.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Users className="w-12 h-12 text-black/60" />
        </div>
      </div>
    ),
    icon: <Users className="h-4 w-4 text-black" />,
  },
  {
    title: "Geschäftslösungen",
    description: "Maßgeschneiderte Kommunikationslösungen für Unternehmen jeder Größe.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Building2 className="w-12 h-12 text-black/60" />
        </div>
      </div>
    ),
    icon: <Building2 className="h-4 w-4 text-black" />,
    className: "md:col-span-2",
  },
  {
    title: "Schnelles Internet",
    description: "DSL, Kabel und Glasfaser bis zu 1000 Mbit/s.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Wifi className="w-12 h-12 text-black/60" />
        </div>
        <div className="absolute bottom-2 right-2 text-xs font-bold text-black/80">
          1000 Mbit/s
        </div>
      </div>
    ),
    icon: <Wifi className="h-4 w-4 text-black" />,
    className: "md:col-span-2",
  },
  {
    title: "Premium Partner",
    description: "Zertifizierter Partner der großen Netzbetreiber.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Award className="w-12 h-12 text-black/60" />
        </div>
      </div>
    ),
    icon: <Award className="h-4 w-4 text-black" />,
  },
  {
    title: "Vor Ort Service",
    description: "Besuchen Sie uns in Gießen oder Limburg.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <MapPin className="w-12 h-12 text-black/60" />
        </div>
      </div>
    ),
    icon: <MapPin className="h-4 w-4 text-black" />,
  },
  {
    title: "Sicherheitslösungen",
    description: "Umfassender Schutz für Ihre Geräte und Daten.",
    header: (
      <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-lg bg-gradient-to-br from-neutral-200 to-neutral-100 relative overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Shield className="w-12 h-12 text-black/60" />
        </div>
      </div>
    ),
    icon: <Shield className="h-4 w-4 text-black" />,
    className: "md:col-span-2",
  },
]

const FeaturesSection: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })

  return (
    <section ref={ref} className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-4">
            <Zap className="w-4 h-4 mr-2" />
            Unsere Stärken
          </span>
          
          <TextGenerateEffect
            words="Warum Kunden die TMC Gruppe wählen"
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-6"
          />
          
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            Entdecken Sie die Vorteile, die uns zu Ihrem idealen Partner für 
            Telekommunikation und digitale Lösungen machen.
          </p>
        </motion.div>

        {/* Bento Grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <BentoGrid className="max-w-4xl mx-auto">
            {features.map((item, i) => (
              <BentoGridItem
                key={i}
                title={item.title}
                description={item.description}
                header={item.header}
                icon={item.icon}
                className={item.className}
              />
            ))}
          </BentoGrid>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-neutral-50 rounded-2xl p-8 md:p-12 border border-black/10">
            <h3 className="text-2xl md:text-3xl font-bold text-black mb-4">
              Überzeugt von unseren Leistungen?
            </h3>
            <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
              Lassen Sie sich persönlich beraten und finden Sie die perfekte Lösung 
              für Ihre Kommunikationsbedürfnisse.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/kontakt"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 bg-black text-white font-medium rounded-lg hover:bg-neutral-800 transition-colors"
              >
                Beratung vereinbaren
              </motion.a>
              <motion.a
                href="/leistungen"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 border border-black text-black font-medium rounded-lg hover:bg-black hover:text-white transition-colors"
              >
                Alle Leistungen
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default FeaturesSection
