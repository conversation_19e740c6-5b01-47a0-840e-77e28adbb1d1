'use client'

import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { 
  Calendar, 
  Users, 
  Award, 
  Target,
  TrendingUp,
  CheckCircle
} from 'lucide-react'
import Card from '@/components/ui/Card'

const timeline = [
  {
    year: '2003',
    title: 'Gründung der TMC Gruppe',
    description: 'Start als lokaler Telekommunikationspartner in Gießen',
    icon: Calendar,
    achievements: ['Erste Partnerschaften', 'Lokale Präsenz']
  },
  {
    year: '2008',
    title: 'Expansion nach Limburg',
    description: 'Eröffnung des zweiten Standorts und Erweiterung des Servicebereichs',
    icon: TrendingUp,
    achievements: ['Zweiter Standort', 'Erweiterte Services']
  },
  {
    year: '2015',
    title: 'Premium Partner Status',
    description: 'Auszeichnung als Premium Partner von o2, Vodafone und Telekom',
    icon: Award,
    achievements: ['Premium Status', 'Alle Netze verfügbar']
  },
  {
    year: '2020',
    title: 'Digitale Transformation',
    description: 'Modernisierung der Services und Einführung digitaler Lösungen',
    icon: Target,
    achievements: ['Online Services', 'Digitale Beratung']
  },
  {
    year: '2024',
    title: 'Heute',
    description: 'Führender Telekommunikationspartner in der Region mit über 1000 zufriedenen Kunden',
    icon: Users,
    achievements: ['1000+ Kunden', 'Marktführer']
  }
]

const stats = [
  { value: '20+', label: 'Jahre Erfahrung', description: 'Seit 2003 am Markt' },
  { value: '1000+', label: 'Zufriedene Kunden', description: 'Vertrauen uns' },
  { value: '4', label: 'Standorte', description: 'In der Region' },
  { value: '3', label: 'Premium Partner', description: 'Alle großen Netze' }
]

const AboutSection: React.FC = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  }

  return (
    <section ref={ref} className="section-padding bg-neutral-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-4">
            <Users className="w-4 h-4 mr-2" />
            Über uns
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-black mb-6">
            Über 20 Jahre{' '}
            <span className="bg-gradient-to-r from-black to-neutral-600 bg-clip-text text-transparent">
              Vertrauen und Expertise
            </span>
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            Seit 2003 sind wir Ihr verlässlicher Partner für moderne Kommunikationslösungen. 
            Von bescheidenen Anfängen zu einem der führenden Telekommunikationspartner in der Region.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              variants={itemVariants}
              className="text-center"
            >
              <Card className="p-6 h-full border-black/10 hover:border-black/20 transition-colors">
                <div className="text-3xl lg:text-4xl font-bold text-black mb-2">
                  {stat.value}
                </div>
                <div className="text-sm font-semibold text-neutral-700 mb-1">
                  {stat.label}
                </div>
                <div className="text-xs text-neutral-500">
                  {stat.description}
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="relative"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-black mb-12 text-center">
            Unsere Erfolgsgeschichte
          </h3>
          
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-black/20 hidden md:block" />
          
          <div className="space-y-12">
            {timeline.map((item, index) => (
              <motion.div
                key={item.year}
                initial={{ opacity: 0, x: -50 }}
                animate={isInView ? { opacity: 1, x: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                className="relative flex items-start space-x-8"
              >
                {/* Timeline Dot */}
                <div className="hidden md:flex items-center justify-center w-16 h-16 bg-black rounded-full text-white font-bold text-sm flex-shrink-0 relative z-10">
                  <item.icon className="w-6 h-6" />
                </div>
                
                {/* Content */}
                <div className="flex-1 md:ml-0 ml-8">
                  <Card className="p-6 border-black/10 hover:border-black/20 transition-all duration-300 hover:shadow-lg">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                      <div>
                        <div className="text-2xl font-bold text-black mb-1">{item.year}</div>
                        <h4 className="text-xl font-semibold text-black">{item.title}</h4>
                      </div>
                      <div className="md:hidden flex items-center justify-center w-12 h-12 bg-black rounded-full text-white mt-2">
                        <item.icon className="w-5 h-5" />
                      </div>
                    </div>
                    
                    <p className="text-neutral-600 mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {item.achievements.map((achievement, idx) => (
                        <span
                          key={idx}
                          className="inline-flex items-center px-3 py-1 rounded-full bg-black/5 text-black text-sm font-medium"
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {achievement}
                        </span>
                      ))}
                    </div>
                  </Card>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Mission Statement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="mt-20 text-center"
        >
          <Card className="p-8 md:p-12 bg-black text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              Unsere Mission
            </h3>
            <p className="text-lg text-white/90 max-w-4xl mx-auto leading-relaxed">
              Wir verbinden Menschen und Unternehmen mit den besten Kommunikationslösungen. 
              Durch persönliche Beratung, innovative Technologien und erstklassigen Service 
              schaffen wir die Grundlage für Ihren digitalen Erfolg. Vertrauen, Qualität 
              und Kundenzufriedenheit stehen dabei immer im Mittelpunkt unseres Handelns.
            </p>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}

export default AboutSection
