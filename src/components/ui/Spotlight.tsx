'use client'

import React, { useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'

interface SpotlightProps {
  className?: string
  fill?: string
}

const Spotlight: React.FC<SpotlightProps> = ({ 
  className,
  fill = 'rgba(0, 0, 0, 0.1)'
}) => {
  const spotlightRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!spotlightRef.current) return

      const rect = spotlightRef.current.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      spotlightRef.current.style.background = `radial-gradient(600px circle at ${x}px ${y}px, ${fill}, transparent 40%)`
    }

    const element = spotlightRef.current?.parentElement
    if (element) {
      element.addEventListener('mousemove', handleMouseMove)
      return () => element.removeEventListener('mousemove', handleMouseMove)
    }
  }, [fill])

  return (
    <div
      ref={spotlightRef}
      className={cn(
        'pointer-events-none absolute inset-0 z-0 transition-all duration-300',
        className
      )}
      style={{
        background: `radial-gradient(600px circle at 50% 50%, ${fill}, transparent 40%)`,
      }}
    />
  )
}

export default Spotlight
