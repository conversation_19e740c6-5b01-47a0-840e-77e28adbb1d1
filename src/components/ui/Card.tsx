'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { CardProps } from '@/types'

const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = false,
  padding = 'md',
  shadow = 'md',
  ...props
}) => {
  const baseClasses = cn(
    'card',
    {
      'card-hover': hover,
      'p-0': padding === 'none',
      'p-3': padding === 'sm',
      'p-6': padding === 'md',
      'p-8': padding === 'lg',
      'shadow-sm': shadow === 'sm',
      'shadow-md': shadow === 'md',
      'shadow-lg': shadow === 'lg',
      'shadow-xl': shadow === 'xl',
      'shadow-none': shadow === 'none',
    },
    className
  )

  if (hover) {
    return (
      <motion.div
        className={baseClasses}
        whileHover={{ 
          y: -4,
          transition: { duration: 0.3, ease: 'easeOut' }
        }}
        initial={{ y: 0 }}
        {...props}
      >
        {children}
      </motion.div>
    )
  }

  return (
    <div className={baseClasses} {...props}>
      {children}
    </div>
  )
}

export default Card
