'use client'

import React, { useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface BackgroundBeamsProps {
  className?: string
}

const BackgroundBeams: React.FC<BackgroundBeamsProps> = ({ className }) => {
  const beamsRef = useRef<SVGSVGElement>(null)

  useEffect(() => {
    const svg = beamsRef.current
    if (!svg) return

    const paths = svg.querySelectorAll('path')
    
    paths.forEach((path, index) => {
      const length = path.getTotalLength()
      path.style.strokeDasharray = `${length}`
      path.style.strokeDashoffset = `${length}`
      
      // Animate each path with different delays
      setTimeout(() => {
        path.style.transition = 'stroke-dashoffset 2s ease-in-out'
        path.style.strokeDashoffset = '0'
      }, index * 200)
    })
  }, [])

  return (
    <div className={cn('absolute inset-0 overflow-hidden', className)}>
      <svg
        ref={beamsRef}
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 1000 1000"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="beam1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="rgba(0,0,0,0)" />
            <stop offset="50%" stopColor="rgba(0,0,0,0.3)" />
            <stop offset="100%" stopColor="rgba(0,0,0,0)" />
          </linearGradient>
          <linearGradient id="beam2" x1="100%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="rgba(0,0,0,0)" />
            <stop offset="50%" stopColor="rgba(0,0,0,0.2)" />
            <stop offset="100%" stopColor="rgba(0,0,0,0)" />
          </linearGradient>
          <linearGradient id="beam3" x1="0%" y1="100%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(0,0,0,0)" />
            <stop offset="50%" stopColor="rgba(0,0,0,0.1)" />
            <stop offset="100%" stopColor="rgba(0,0,0,0)" />
          </linearGradient>
        </defs>
        
        {/* Beam paths */}
        <path
          d="M0,200 Q250,100 500,200 T1000,200"
          stroke="url(#beam1)"
          strokeWidth="2"
          fill="none"
          opacity="0.6"
        />
        <path
          d="M0,400 Q300,300 600,400 T1000,400"
          stroke="url(#beam2)"
          strokeWidth="1.5"
          fill="none"
          opacity="0.4"
        />
        <path
          d="M0,600 Q200,500 400,600 T1000,600"
          stroke="url(#beam3)"
          strokeWidth="1"
          fill="none"
          opacity="0.3"
        />
        <path
          d="M0,800 Q400,700 800,800 T1000,800"
          stroke="url(#beam1)"
          strokeWidth="1.5"
          fill="none"
          opacity="0.2"
        />
        
        {/* Additional decorative paths */}
        <path
          d="M200,0 Q300,250 400,500 T600,1000"
          stroke="url(#beam2)"
          strokeWidth="1"
          fill="none"
          opacity="0.1"
        />
        <path
          d="M800,0 Q700,300 600,600 T400,1000"
          stroke="url(#beam3)"
          strokeWidth="1"
          fill="none"
          opacity="0.1"
        />
      </svg>
      
      {/* Animated dots */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-black/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>
    </div>
  )
}

export default BackgroundBeams
