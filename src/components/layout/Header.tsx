'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Menu, 
  X, 
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  ChevronDown,
  Smartphone,
  Wifi,
  Shield,
  Building2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Button from '@/components/ui/Button'
import type { NavigationItem } from '@/types'

const navigation: NavigationItem[] = [
  {
    label: 'Startseite',
    href: '/',
  },
  {
    label: 'Leistungen',
    href: '/leistungen',
    children: [
      {
        label: 'Mobilfunk',
        href: '/leistungen/mobilfunk',
        icon: 'Smartphone',
        description: 'Tarife und Geräte für jeden Bedarf'
      },
      {
        label: 'Internet & Festnetz',
        href: '/leistungen/internet',
        icon: 'Wifi',
        description: 'DSL, Kabel und Glasfaser'
      },
      {
        label: 'Geschäftskunden',
        href: '/leistungen/business',
        icon: 'Building2',
        description: 'Maßgeschneiderte Lösungen'
      },
      {
        label: 'Sicherheit',
        href: '/leistungen/sicherheit',
        icon: 'Shield',
        description: 'Schutz für Geräte und Daten'
      }
    ]
  },
  {
    label: 'Standorte',
    href: '/standorte',
  },
  {
    label: 'Über uns',
    href: '/ueber-uns',
  },
  {
    label: 'Kontakt',
    href: '/kontakt',
  }
]

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isMobileMenuOpen])

  const iconMap = {
    Smartphone,
    Wifi,
    Building2,
    Shield
  }

  return (
    <>
      {/* Top Bar */}
      <div className="bg-black text-white py-2 text-sm hidden md:block">
        <div className="container-custom">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>Mo-Fr: 10:00-18:30 | Sa: 10:00-18:00</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <a
                href="tel:+4964198389836"
                className="flex items-center space-x-1 hover:text-neutral-300 transition-colors"
              >
                <Phone className="w-4 h-4" />
                <span>0641 – 98389836</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-1 hover:text-neutral-300 transition-colors"
              >
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <motion.header
        className={cn(
          'sticky top-0 z-50 transition-all duration-300',
          isScrolled 
            ? 'bg-white/95 backdrop-blur-md shadow-lg' 
            : 'bg-white'
        )}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        <div className="container-custom">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative w-12 h-12">
                <Image
                  src="/logo.svg"
                  alt="TMC Gruppe Logo"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-primary-900">TMC Gruppe</h1>
                <p className="text-sm text-neutral-600">Ihr Partner für moderne Kommunikation</p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navigation.map((item) => (
                <div
                  key={item.label}
                  className="relative"
                  onMouseEnter={() => item.children && setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  <Link
                    href={item.href}
                    className={cn(
                      'flex items-center space-x-1 px-3 py-2 rounded-lg font-medium transition-all duration-200',
                      'text-neutral-700 hover:text-black hover:bg-neutral-50'
                    )}
                  >
                    <span>{item.label}</span>
                    {item.children && (
                      <ChevronDown className="w-4 h-4 transition-transform duration-200" />
                    )}
                  </Link>

                  {/* Dropdown Menu */}
                  <AnimatePresence>
                    {item.children && activeDropdown === item.label && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-neutral-200 overflow-hidden"
                      >
                        <div className="p-4">
                          {item.children.map((child) => {
                            const IconComponent = iconMap[child.icon as keyof typeof iconMap]
                            return (
                              <Link
                                key={child.label}
                                href={child.href}
                                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-neutral-50 transition-colors group"
                              >
                                {IconComponent && (
                                  <div className="flex-shrink-0 w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                                    <IconComponent className="w-5 h-5 text-primary-600" />
                                  </div>
                                )}
                                <div>
                                  <h4 className="font-medium text-neutral-900 group-hover:text-primary-600 transition-colors">
                                    {child.label}
                                  </h4>
                                  {child.description && (
                                    <p className="text-sm text-neutral-600 mt-1">
                                      {child.description}
                                    </p>
                                  )}
                                </div>
                              </Link>
                            )
                          })}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </nav>

            {/* CTA Button */}
            <div className="hidden lg:block">
              <Button href="/kontakt" variant="primary" size="md">
                Beratung anfragen
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-neutral-100 transition-colors"
              aria-label="Menu öffnen"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="lg:hidden bg-white border-t border-neutral-200"
            >
              <div className="container-custom py-6">
                <nav className="space-y-4">
                  {navigation.map((item) => (
                    <div key={item.label}>
                      <Link
                        href={item.href}
                        className="block px-4 py-3 rounded-lg font-medium text-neutral-700 hover:text-primary-600 hover:bg-primary-50 transition-colors"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.label}
                      </Link>
                      {item.children && (
                        <div className="ml-4 mt-2 space-y-2">
                          {item.children.map((child) => (
                            <Link
                              key={child.label}
                              href={child.href}
                              className="block px-4 py-2 text-sm text-neutral-600 hover:text-primary-600 transition-colors"
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              {child.label}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </nav>
                <div className="mt-6 pt-6 border-t border-neutral-200">
                  <Button 
                    href="/kontakt" 
                    variant="primary" 
                    size="lg" 
                    className="w-full"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Beratung anfragen
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.header>
    </>
  )
}

export default Header
