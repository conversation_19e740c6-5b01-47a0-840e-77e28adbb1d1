'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Facebook, 
  Instagram, 
  MessageCircle,
  ExternalLink
} from 'lucide-react'

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    services: [
      { label: 'Mobilfunk', href: '/leistungen/mobilfunk' },
      { label: 'Internet & Festnetz', href: '/leistungen/internet' },
      { label: 'Geschäftskunden', href: '/leistungen/business' },
      { label: 'Sicherheit', href: '/leistungen/sicherheit' },
    ],
    company: [
      { label: 'Über uns', href: '/ueber-uns' },
      { label: 'Standorte', href: '/standorte' },
      { label: 'Karriere', href: '/karriere' },
      { label: 'Presse', href: '/presse' },
    ],
    support: [
      { label: 'Kontakt', href: '/kontakt' },
      { label: 'FAQ', href: '/faq' },
      { label: 'Hilfe & Support', href: '/support' },
      { label: 'Reparatur-Service', href: '/reparatur' },
    ],
    legal: [
      { label: 'Impressum', href: '/impressum' },
      { label: 'Datenschutz', href: '/datenschutz' },
      { label: 'AGB', href: '/agb' },
      { label: 'Widerrufsrecht', href: '/widerruf' },
    ]
  }

  const socialLinks = [
    {
      name: 'Facebook',
      href: 'https://www.facebook.com/o2giessen1',
      icon: Facebook
    },
    {
      name: 'Instagram',
      href: 'https://www.instagram.com/o2shopgiessen/',
      icon: Instagram
    },
    {
      name: 'WhatsApp',
      href: 'https://wa.me/4964198389836',
      icon: MessageCircle
    }
  ]

  const locations = [
    {
      name: 'o2 Shop Gießen',
      address: 'Neustadt 8, 35390 Gießen',
      phone: '+49 641 98389836',
      email: '<EMAIL>'
    },
    {
      name: 'Telemediacenter Gießen',
      address: 'Neustadt 6, 35390 Gießen',
      phone: '+49 641 92369999',
      email: '<EMAIL>'
    },
    {
      name: 'Spartarif24 Gießen',
      address: 'Bahnhofstraße 12, 35390 Gießen',
      phone: '+49 641 97272296',
      email: '<EMAIL>'
    },
    {
      name: 'Telemediacenter Limburg',
      address: 'Bahnhofstraße 4, 65549 Limburg',
      phone: '+49 6431 590562',
      email: '<EMAIL>'
    }
  ]

  return (
    <footer className="bg-black text-white">
      {/* Main Footer Content */}
      <div className="container-custom py-16">
        <div className="grid lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-3 mb-6">
              <div className="relative w-10 h-10">
                <Image
                  src="/logo.svg"
                  alt="TMC Gruppe Logo"
                  fill
                  className="object-contain filter invert"
                />
              </div>
              <div>
                <h3 className="text-xl font-bold">TMC Gruppe</h3>
                <p className="text-sm text-white/70">Ihr Partner für moderne Kommunikation</p>
              </div>
            </Link>
            
            <p className="text-white/80 mb-6 leading-relaxed">
              Seit 2003 sind wir Ihr vertrauensvoller Partner für Telekommunikation 
              in der Region. Als Premium Partner von o2, Vodafone und Telekom bieten 
              wir Ihnen erstklassige Beratung und Service.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Leistungen</h4>
            <ul className="space-y-2">
              {footerLinks.services.map((link) => (
                <li key={link.label}>
                  <Link 
                    href={link.href}
                    className="text-white/70 hover:text-white transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Unternehmen</h4>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.label}>
                  <Link 
                    href={link.href}
                    className="text-white/70 hover:text-white transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Support</h4>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.label}>
                  <Link 
                    href={link.href}
                    className="text-white/70 hover:text-white transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Kontakt</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Phone className="w-5 h-5 text-white/70 mt-0.5 flex-shrink-0" />
                <div>
                  <a 
                    href="tel:+4964198389836"
                    className="text-white/70 hover:text-white transition-colors"
                  >
                    0641 – 98389836
                  </a>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Mail className="w-5 h-5 text-white/70 mt-0.5 flex-shrink-0" />
                <div>
                  <a 
                    href="mailto:<EMAIL>"
                    className="text-white/70 hover:text-white transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-white/70 mt-0.5 flex-shrink-0" />
                <div className="text-white/70 text-sm">
                  <div>Mo-Fr: 10:00-18:30</div>
                  <div>Sa: 10:00-18:00</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Locations */}
        <div className="border-t border-white/20 mt-12 pt-8">
          <h4 className="text-lg font-semibold mb-6">Unsere Standorte</h4>
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {locations.map((location) => (
              <div key={location.name} className="bg-white/5 rounded-lg p-4">
                <h5 className="font-semibold mb-2 text-sm">{location.name}</h5>
                <div className="space-y-2 text-xs text-white/70">
                  <div className="flex items-start space-x-2">
                    <MapPin className="w-3 h-3 mt-0.5 flex-shrink-0" />
                    <span>{location.address}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-3 h-3 flex-shrink-0" />
                    <a
                      href={`tel:${location.phone}`}
                      className="hover:text-white transition-colors"
                    >
                      {location.phone}
                    </a>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-3 h-3 flex-shrink-0" />
                    <a
                      href={`mailto:${location.email}`}
                      className="hover:text-white transition-colors break-all"
                    >
                      {location.email}
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-white/20">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-white/70">
              © {currentYear} TMC Gruppe. Alle Rechte vorbehalten.
            </div>
            
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              {footerLinks.legal.map((link) => (
                <Link 
                  key={link.label}
                  href={link.href}
                  className="text-white/70 hover:text-white transition-colors"
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
