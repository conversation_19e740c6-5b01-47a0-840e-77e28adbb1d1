import React from 'react'
import type { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Phone, Mail, MapPin, Clock, MessageCircle } from 'lucide-react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export const metadata: Metadata = {
  title: 'Kontakt - TMC Gruppe',
  description: 'Kontaktieren Sie die TMC Gruppe für eine persönliche Beratung zu Mobilfunk, Internet und Geschäftslösungen.',
}

export default function ContactPage() {
  return (
    <main className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-black mb-6">
              Kontaktieren Sie{' '}
              <span className="bg-gradient-to-r from-black to-neutral-600 bg-clip-text text-transparent">
                uns
              </span>
            </h1>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Wir sind für Sie da! Kontaktieren Sie uns für eine persönliche Beratung 
              oder besuchen Sie uns in einer unserer Geschäftsstellen.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="pb-16">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Phone */}
            <Card className="p-6 text-center border-black/10 hover:border-black/20 transition-colors">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">Telefon</h3>
              <p className="text-neutral-600 mb-4">
                Rufen Sie uns an für eine sofortige Beratung
              </p>
              <Button
                variant="outline"
                href="tel:+*************"
                className="border-black text-black hover:bg-black hover:text-white"
              >
                0641 – 98389836
              </Button>
            </Card>

            {/* Email */}
            <Card className="p-6 text-center border-black/10 hover:border-black/20 transition-colors">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">E-Mail</h3>
              <p className="text-neutral-600 mb-4">
                Schreiben Sie uns Ihr Anliegen
              </p>
              <Button
                variant="outline"
                href="mailto:<EMAIL>"
                className="border-black text-black hover:bg-black hover:text-white"
              >
                <EMAIL>
              </Button>
            </Card>

            {/* WhatsApp */}
            <Card className="p-6 text-center border-black/10 hover:border-black/20 transition-colors md:col-span-2 lg:col-span-1">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">WhatsApp</h3>
              <p className="text-neutral-600 mb-4">
                Schnelle Hilfe über WhatsApp
              </p>
              <Button
                variant="outline"
                href="https://wa.me/*************"
                target="_blank"
                className="border-black text-black hover:bg-black hover:text-white"
              >
                WhatsApp öffnen
              </Button>
            </Card>
          </div>

          {/* Locations */}
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* o2 Shop Gießen */}
            <Card className="p-4 border-black/10">
              <div className="flex items-start space-x-3 mb-3">
                <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-black mb-1">o2 Shop Gießen</h3>
                  <div className="text-neutral-600 text-sm">
                    <div>Neustadt 8</div>
                    <div>35390 Gießen</div>
                  </div>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="text-xs font-medium text-black">Öffnungszeiten</div>
                <div className="text-xs text-neutral-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Mo-Fr:</span>
                    <span>10:00-18:30</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sa:</span>
                    <span>10:00-18:00</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  href="tel:+*************"
                  className="w-full border-black text-black hover:bg-black hover:text-white text-xs"
                >
                  0641 – 98389836
                </Button>
              </div>
            </Card>

            {/* Telemediacenter Gießen */}
            <Card className="p-4 border-black/10">
              <div className="flex items-start space-x-3 mb-3">
                <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-black mb-1">Telemediacenter Gießen</h3>
                  <div className="text-neutral-600 text-sm">
                    <div>Neustadt 6</div>
                    <div>35390 Gießen</div>
                  </div>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="text-xs font-medium text-black">Öffnungszeiten</div>
                <div className="text-xs text-neutral-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Mo-Fr:</span>
                    <span>10:00-18:30</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sa:</span>
                    <span>10:00-18:00</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  href="tel:+*************"
                  className="w-full border-black text-black hover:bg-black hover:text-white text-xs"
                >
                  0641 – 92369999
                </Button>
              </div>
            </Card>

            {/* Spartarif24 Gießen */}
            <Card className="p-4 border-black/10">
              <div className="flex items-start space-x-3 mb-3">
                <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-black mb-1">Spartarif24 Gießen</h3>
                  <div className="text-neutral-600 text-sm">
                    <div>Bahnhofstraße 12</div>
                    <div>35390 Gießen</div>
                  </div>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="text-xs font-medium text-black">Öffnungszeiten</div>
                <div className="text-xs text-neutral-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Mo-Fr:</span>
                    <span>10:00-18:30</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sa:</span>
                    <span>10:00-18:00</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  href="tel:+*************"
                  className="w-full border-black text-black hover:bg-black hover:text-white text-xs"
                >
                  0641 – 97272296
                </Button>
              </div>
            </Card>

            {/* Telemediacenter Limburg */}
            <Card className="p-4 border-black/10">
              <div className="flex items-start space-x-3 mb-3">
                <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-black mb-1">Telemediacenter Limburg</h3>
                  <div className="text-neutral-600 text-sm">
                    <div>Bahnhofstraße 4</div>
                    <div>65549 Limburg</div>
                  </div>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="text-xs font-medium text-black">Öffnungszeiten</div>
                <div className="text-xs text-neutral-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Mo-Fr:</span>
                    <span>10:30-19:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sa:</span>
                    <span>10:30-17:00</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  href="tel:+496431590562"
                  className="w-full border-black text-black hover:bg-black hover:text-white text-xs"
                >
                  06431 – 590562
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-neutral-50">
        <div className="container-custom">
          <Card className="p-8 md:p-12 bg-black text-white text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Bereit für eine persönliche Beratung?
            </h2>
            <p className="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Unser Team steht Ihnen gerne zur Verfügung. Vereinbaren Sie einen Termin 
              oder besuchen Sie uns spontan in einer unserer Geschäftsstellen.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                href="tel:+*************"
                className="bg-white text-black hover:bg-neutral-100"
              >
                <Phone className="w-5 h-5 mr-2" />
                Jetzt anrufen
              </Button>
              <Button
                variant="outline"
                size="lg"
                href="/standorte"
                className="border-white text-white hover:bg-white hover:text-black"
              >
                <MapPin className="w-5 h-5 mr-2" />
                Standorte ansehen
              </Button>
            </div>
          </Card>
        </div>
      </section>

      <Footer />
    </main>
  )
}
