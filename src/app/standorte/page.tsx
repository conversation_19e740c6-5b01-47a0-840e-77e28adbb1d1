import React from 'react'
import type { Metadata } from 'next'
import Header from '@/components/layout/Header'
import LocationsSection from '@/components/sections/LocationsSection'

export const metadata: Metadata = {
  title: 'Standorte - TMC Gruppe',
  description: 'Besuchen Sie uns in unseren Geschäftsstellen in Gießen und Limburg. Persönliche Beratung vor Ort.',
}

export default function LocationsPage() {
  return (
    <main className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-black mb-6">
              Unsere{' '}
              <span className="bg-gradient-to-r from-black to-neutral-600 bg-clip-text text-transparent">
                Standorte
              </span>
            </h1>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Besuchen Sie uns in einer unserer modernen Geschäftsstellen in Gießen und Limburg. 
              Unser erfahrenes Team berät Sie gerne persönlich vor Ort.
            </p>
          </div>
        </div>
      </section>

      <LocationsSection />
    </main>
  )
}
