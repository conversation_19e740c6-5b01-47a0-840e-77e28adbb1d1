@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 0%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-neutral-200;
  }

  body {
    @apply bg-white text-neutral-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400;
  }

  /* Focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white active:bg-primary-700;
  }

  .btn-ghost {
    @apply text-neutral-700 hover:bg-neutral-100 active:bg-neutral-200;
  }

  /* Size variants */
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-md {
    @apply px-4 py-2 text-base;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  .btn-xl {
    @apply px-8 py-4 text-xl;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-neutral-200 overflow-hidden;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white/80 backdrop-blur-md border border-white/20;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600;
  }

  .gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600;
  }

  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Container styles */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Section spacing */
  .section-padding {
    @apply py-16 lg:py-24;
  }

  /* Animation utilities */
  .animate-on-scroll {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .animate-on-scroll.in-view {
    @apply opacity-100 translate-y-0;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-neutral-200 rounded;
  }

  /* Responsive text */
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-md {
    @apply text-base sm:text-lg lg:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl xl:text-3xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Aspect ratio utilities */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Infinite scroll animation */
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-100% - 1rem));
    }
  }

  .animate-scroll {
    animation: scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite;
  }

  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-12 sm:py-16 md:py-20 lg:py-24;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    .container-custom {
      @apply px-3;
    }

    .section-padding {
      @apply py-8;
    }
  }
}
