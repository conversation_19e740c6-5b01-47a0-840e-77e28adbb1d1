import React from 'react'
import type { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import HeroSection from '@/components/sections/HeroSection'
import FeaturesSection from '@/components/sections/FeaturesSection'
import ServicesSection from '@/components/sections/ServicesSection'
import AboutSection from '@/components/sections/AboutSection'
import TestimonialsSection from '@/components/sections/TestimonialsSection'
import LocationsSection from '@/components/sections/LocationsSection'
import FAQSection from '@/components/sections/FAQSection'

export const metadata: Metadata = {
  title: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
  description: 'Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv und bietet sowohl Privat- als auch Geschäftskunden qualifizierte und kompetente Beratung in den Bereichen Mobilfunk, Festnetz und Internet.',
  keywords: ['TMC Gruppe', 'Mobilfunk', 'Internet', 'Telekom', 'o2', 'Vodafone', 'Gießen', 'Limburg'],
  openGraph: {
    title: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
    description: 'Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv.',
    type: 'website',
    locale: 'de_DE',
    url: 'https://www.tmc-gruppe.de',
    siteName: 'TMC Gruppe',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
    description: 'Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv.',
  },
  alternates: {
    canonical: 'https://www.tmc-gruppe.de',
  },
}

export default function HomePage() {
  return (
    <main className="min-h-screen">
      <Header />
      <HeroSection />
      <FeaturesSection />
      <ServicesSection />
      <AboutSection />
      <TestimonialsSection />
      <LocationsSection />
      <FAQSection />
      <Footer />
    </main>
  )
}
