import React from 'react'
import type { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Smartphone, Check, Star, Users } from 'lucide-react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export const metadata: Metadata = {
  title: 'Mobilfunk - TMC Gruppe',
  description: 'Entdecken Sie unsere Mobilfunktarife von o2, Vodafone und Telekom. Flexible Laufzeiten, hohe Datenvolumen und neueste Smartphones.',
}

const networks = [
  {
    name: 'o2',
    description: 'Günstige Tarife mit flexiblen Optionen',
    features: ['Unlimited Tarife', 'EU-Roaming inklusive', 'Flexible Laufzeiten']
  },
  {
    name: 'Vodafone',
    description: 'Starkes Netz mit 5G-Abdeckung',
    features: ['5G-Geschwindigkeit', 'GigaTV verfügbar', 'Red-Tarife']
  },
  {
    name: 'Telekom',
    description: 'Deutschlands bestes Netz',
    features: ['Beste Netzabdeckung', 'MagentaEINS Vorteile', 'Premium Service']
  }
]

const features = [
  'Alle deutschen Mobilfunknetze verfügbar',
  'Kostenlose Rufnummernmitnahme',
  'Persönliche Beratung vor Ort',
  'Schnelle Aktivierung innerhalb 24h',
  'Neueste Smartphones verfügbar',
  'Flexible Vertragslaufzeiten',
  'EU-Roaming inklusive',
  'Familien- und Geschäftstarife'
]

export default function MobilfunkPage() {
  return (
    <main className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-white">
        <div className="container-custom">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-black text-white text-sm font-medium mb-4">
              <Smartphone className="w-4 h-4 mr-2" />
              Mobilfunk
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-black mb-6">
              Mobilfunk für{' '}
              <span className="bg-gradient-to-r from-black to-neutral-600 bg-clip-text text-transparent">
                jeden Bedarf
              </span>
            </h1>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Entdecken Sie unsere Mobilfunktarife von o2, Vodafone und Telekom. 
              Wir finden den perfekten Tarif für Ihre individuellen Bedürfnisse.
            </p>
          </div>
        </div>
      </section>

      {/* Networks Section */}
      <section className="pb-16">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-black text-center mb-12">
            Alle großen Netze verfügbar
          </h2>
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {networks.map((network) => (
              <Card key={network.name} className="p-6 text-center border-black/10 hover:border-black/20 transition-colors">
                <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-lg">{network.name}</span>
                </div>
                <h3 className="text-xl font-bold text-black mb-2">{network.name}</h3>
                <p className="text-neutral-600 mb-4">{network.description}</p>
                <ul className="space-y-2">
                  {network.features.map((feature, index) => (
                    <li key={index} className="flex items-center justify-center text-sm text-neutral-600">
                      <Check className="w-4 h-4 text-green-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="pb-16 bg-neutral-50">
        <div className="container-custom py-16">
          <h2 className="text-3xl font-bold text-black text-center mb-12">
            Ihre Vorteile bei TMC
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-neutral-700">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="pb-16">
        <div className="container-custom">
          <Card className="p-8 md:p-12 bg-black text-white text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Bereit für Ihren neuen Mobilfunkvertrag?
            </h2>
            <p className="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Lassen Sie sich von unseren Experten beraten und finden Sie den 
              perfekten Tarif für Ihre Bedürfnisse.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                href="/kontakt"
                className="bg-white text-black hover:bg-neutral-100"
              >
                Beratung vereinbaren
              </Button>
              <Button
                variant="outline"
                size="lg"
                href="tel:+*************"
                className="border-white text-white hover:bg-white hover:text-black"
              >
                Jetzt anrufen
              </Button>
            </div>
          </Card>
        </div>
      </section>

      <Footer />
    </main>
  )
}
