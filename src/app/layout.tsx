import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
    template: '%s | TMC Gruppe'
  },
  description: 'Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv und bietet sowohl Privat- als auch Geschäftskunden qualifizierte und kompetente Beratung in den Bereichen Mobilfunk, Festnetz und Internet.',
  keywords: ['TMC Gruppe', 'Mobilfunk', 'Internet', 'Telekom', 'o2', 'Vodafone', '<PERSON><PERSON><PERSON><PERSON>', 'Lim<PERSON>', 'Geschäftskunden', 'Privatkund<PERSON>'],
  authors: [{ name: 'TMC Gruppe' }],
  creator: 'TMC Gruppe',
  publisher: 'TMC Gruppe',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://www.tmc-gruppe.de'),
  alternates: {
    canonical: '/',
    languages: {
      'de-DE': '/de',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'de_DE',
    url: 'https://www.tmc-gruppe.de',
    title: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
    description: 'Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv und bietet sowohl Privat- als auch Geschäftskunden qualifizierte und kompetente Beratung.',
    siteName: 'TMC Gruppe',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TMC Gruppe - Ihr Partner für moderne Kommunikation',
    description: 'Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="de" className={`${inter.variable} ${poppins.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className="font-sans antialiased bg-white text-neutral-900 overflow-x-hidden">
        <div id="root">
          {children}
        </div>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "TMC Gruppe",
              "url": "https://www.tmc-gruppe.de",
              "logo": "https://www.tmc-gruppe.de/logo.png",
              "description": "Seit 2003 ist die TMC-Gruppe als Partner von o2, Vodafone und Telekom in der Region aktiv.",
              "address": [
                {
                  "@type": "PostalAddress",
                  "streetAddress": "Neustadt 8",
                  "addressLocality": "Gießen",
                  "postalCode": "35390",
                  "addressCountry": "DE"
                },
                {
                  "@type": "PostalAddress",
                  "streetAddress": "Bahnhofstraße 4",
                  "addressLocality": "Limburg an der Lahn",
                  "postalCode": "65549",
                  "addressCountry": "DE"
                }
              ],
              "telephone": "+49-641-98389836",
              "email": "<EMAIL>",
              "sameAs": [
                "https://www.facebook.com/o2giessen1",
                "https://www.instagram.com/o2shopgiessen/",
                "https://www.tiktok.com/@o2shopgiessen"
              ]
            })
          }}
        />
      </body>
    </html>
  )
}
