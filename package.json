{"name": "tmc-gruppe-website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@react-three/drei": "^9.114.0", "@react-three/fiber": "^8.17.0", "clsx": "^2.1.1", "framer-motion": "^11.11.0", "gsap": "^3.12.0", "lucide-react": "^0.460.0", "mini-svg-data-uri": "^1.4.4", "motion": "^12.23.9", "next": "^14.2.30", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.13.0", "simplex-noise": "^4.0.1", "tailwind-merge": "^2.6.0", "three": "^0.169.0"}, "devDependencies": {"@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/three": "^0.169.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.30", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "^5.6.0"}, "engines": {"node": ">=18.0.0"}}