version: '3.8'

services:
  # TMC Gruppe Website
  tmc-website:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: tmc-gruppe-website
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3000
      - HOSTNAME=0.0.0.0
    networks:
      - tmc-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.tmc-website.rule=Host(`www.tmc-gruppe.de`)"
      - "traefik.http.routers.tmc-website.tls=true"
      - "traefik.http.routers.tmc-website.tls.certresolver=letsencrypt"
      - "traefik.http.services.tmc-website.loadbalancer.server.port=3000"
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Reverse Proxy (Optional - for production with SSL)
  traefik:
    image: traefik:v2.10
    container_name: tmc-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080" # Traefik dashboard
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --log.level=INFO
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
    networks:
      - tmc-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.tmc-gruppe.de`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"

  # Redis for caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: tmc-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tmc-network
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

networks:
  tmc-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
