#!/bin/bash

# TMC Gruppe Website Setup Script
echo "🚀 Setting up TMC Gruppe Website..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create necessary directories
echo "📁 Creating project structure..."
mkdir -p public/images
mkdir -p src/components/ui
mkdir -p src/components/layout
mkdir -p src/components/sections
mkdir -p src/lib
mkdir -p src/types
mkdir -p src/styles

# Create placeholder logo if it doesn't exist
if [ ! -f "public/logo.png" ]; then
    echo "🎨 Creating placeholder logo..."
    # You would typically copy your actual logo here
    touch public/logo.png
fi

# Create favicon if it doesn't exist
if [ ! -f "public/favicon.ico" ]; then
    echo "🎨 Creating placeholder favicon..."
    touch public/favicon.ico
fi

# Run type check
echo "🔍 Running type check..."
npm run type-check

if [ $? -ne 0 ]; then
    echo "⚠️  Type check failed, but continuing..."
fi

# Run linting
echo "🧹 Running linter..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  Linting issues found, but continuing..."
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Start the development server: npm run dev"
echo "2. Open http://localhost:3000 in your browser"
echo "3. Start developing your amazing website!"
echo ""
echo "📚 Available commands:"
echo "  npm run dev      - Start development server"
echo "  npm run build    - Build for production"
echo "  npm run start    - Start production server"
echo "  npm run lint     - Run ESLint"
echo "  npm run type-check - Run TypeScript check"
echo ""
echo "🔗 Useful links:"
echo "  - Documentation: README.md"
echo "  - Implementation Plan: IMPLEMENTATION_PLAN.md"
echo "  - Design System: src/app/globals.css"
echo ""
echo "Happy coding! 🚀"
