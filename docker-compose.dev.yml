version: '3.8'

services:
  # Development version with hot reload
  tmc-website-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: tmc-gruppe-website-dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
    networks:
      - tmc-dev-network
    command: npm run dev

  # Development database (if needed)
  postgres-dev:
    image: postgres:15-alpine
    container_name: tmc-postgres-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=tmc_dev
      - POSTGRES_USER=tmc_user
      - POSTGRES_PASSWORD=tmc_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - tmc-dev-network

  # Development Redis
  redis-dev:
    image: redis:7-alpine
    container_name: tmc-redis-dev
    ports:
      - "6379:6379"
    networks:
      - tmc-dev-network

networks:
  tmc-dev-network:
    driver: bridge

volumes:
  postgres_dev_data:
    driver: local
