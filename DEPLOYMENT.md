# TMC Gruppe Website - Deployment Guide

## 🐳 Docker Deployment

### Quick Start

```bash
# 1. Build and deploy production
./deploy.sh

# 2. Development environment
./deploy.sh --env development

# 3. Build only (no deployment)
./deploy.sh --build-only
```

## 📋 Prerequisites

- **Docker** 20.10+ and **Docker Compose** 2.0+
- **Node.js** 18+ (for local development)
- **Git** for version control

## 🏗️ Build Options

### Production Build
```bash
# Multi-stage optimized build
docker build -t tmc-gruppe-website:latest .

# Or using deploy script
./deploy.sh --env production
```

### Development Build
```bash
# Development with hot reload
docker build -f Dockerfile.dev -t tmc-gruppe-website:dev .

# Or using docker-compose
docker-compose -f docker-compose.dev.yml up
```

## 🚀 Deployment Environments

### 1. Local Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Access at: http://localhost:3000
# Hot reload enabled
# Development tools included
```

### 2. Production Deployment
```bash
# Full production stack with SSL
docker-compose up -d

# Includes:
# - Next.js application
# - Traefik reverse proxy
# - SSL certificates (Let's Encrypt)
# - Redis caching
# - Health checks
```

## 🔧 Configuration

### Environment Variables
```bash
# Copy example environment file
cp .env.example .env.local

# Edit with your values
nano .env.local
```

### Key Configuration Options
```env
# Application
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://www.tmc-gruppe.de

# Contact Information
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=+4964198389836

# Google Services
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 🌐 Domain & SSL Setup

### Traefik Configuration
The included Traefik setup automatically handles:
- SSL certificate generation (Let's Encrypt)
- HTTP to HTTPS redirect
- Load balancing
- Health checks

### DNS Configuration
Point your domain to the server:
```
A     www.tmc-gruppe.de    → YOUR_SERVER_IP
CNAME tmc-gruppe.de       → www.tmc-gruppe.de
```

## 📊 Monitoring & Health Checks

### Health Check Endpoint
```bash
# Check application health
curl http://localhost:3000/api/health

# Response:
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "memory": {
    "used": 45,
    "total": 128
  }
}
```

### Container Health
```bash
# Check container status
docker ps

# View logs
docker-compose logs -f tmc-website

# Container stats
docker stats tmc-gruppe-website
```

## 🔄 CI/CD Pipeline

### GitHub Actions (Recommended)
```yaml
# .github/workflows/deploy.yml
name: Deploy TMC Website

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Build and Deploy
        run: |
          docker build -t tmc-website .
          # Deploy to your server
```

### Manual Deployment
```bash
# 1. Pull latest code
git pull origin main

# 2. Build and deploy
./deploy.sh

# 3. Verify deployment
curl -f http://localhost:3000/api/health
```

## 🛠️ Maintenance Commands

### Container Management
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart tmc-website

# View logs
docker-compose logs -f

# Shell access
docker exec -it tmc-gruppe-website sh
```

### Updates & Maintenance
```bash
# Update application
git pull
./deploy.sh

# Update Docker images
docker-compose pull
docker-compose up -d

# Clean up old images
docker image prune -f
```

## 📈 Performance Optimization

### Production Optimizations
- **Multi-stage build** reduces image size by 70%
- **Standalone output** for minimal runtime
- **Compression** enabled for all assets
- **Image optimization** with WebP/AVIF
- **Redis caching** for API responses

### Resource Limits
```yaml
# docker-compose.yml
services:
  tmc-website:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

## 🔒 Security

### Security Headers
Automatically configured:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `Strict-Transport-Security`
- `Referrer-Policy: origin-when-cross-origin`

### Container Security
- **Non-root user** (nextjs:nodejs)
- **Minimal base image** (Alpine Linux)
- **No unnecessary packages**
- **Read-only filesystem** where possible

## 🚨 Troubleshooting

### Common Issues

#### Container won't start
```bash
# Check logs
docker-compose logs tmc-website

# Check resource usage
docker stats

# Verify environment variables
docker exec tmc-gruppe-website env
```

#### SSL Certificate Issues
```bash
# Check Traefik logs
docker-compose logs traefik

# Verify domain DNS
nslookup www.tmc-gruppe.de

# Manual certificate generation
docker exec traefik-container acme.sh --issue -d www.tmc-gruppe.de
```

#### Performance Issues
```bash
# Check memory usage
docker stats tmc-gruppe-website

# Analyze bundle size
npm run analyze

# Check Redis cache
docker exec tmc-redis redis-cli info memory
```

### Log Analysis
```bash
# Application logs
docker-compose logs -f tmc-website

# System logs
journalctl -u docker

# Nginx/Traefik logs
docker-compose logs traefik
```

## 📞 Support

For deployment issues:
- **Email**: <EMAIL>
- **Documentation**: README.md
- **Health Check**: http://your-domain/api/health

## 🎯 Deployment Checklist

- [ ] Environment variables configured
- [ ] DNS records pointing to server
- [ ] SSL certificates working
- [ ] Health checks passing
- [ ] Performance metrics acceptable
- [ ] Monitoring setup
- [ ] Backup strategy in place
- [ ] Security headers verified

---

**Ready to deploy? Run `./deploy.sh` and watch your website come to life! 🚀**
