#!/bin/bash

# TMC Gruppe Website Deployment Script
set -e

echo "🚀 TMC Gruppe Website Deployment"
echo "================================="

# Configuration
IMAGE_NAME="tmc-gruppe-website"
CONTAINER_NAME="tmc-website"
PORT="3000"
DOMAIN="www.tmc-gruppe.de"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    log_error "Docker is not running. Please start Docker first."
    exit 1
fi

log_success "Docker is running"

# Parse command line arguments
ENVIRONMENT="production"
BUILD_ONLY=false
SKIP_BUILD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --env ENV        Set environment (development|production) [default: production]"
            echo "  --build-only     Only build the image, don't deploy"
            echo "  --skip-build     Skip building, use existing image"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

log_info "Environment: $ENVIRONMENT"

# Build the Docker image
if [ "$SKIP_BUILD" = false ]; then
    log_info "Building Docker image..."
    
    if [ "$ENVIRONMENT" = "development" ]; then
        docker build -f Dockerfile.dev -t $IMAGE_NAME:dev .
        IMAGE_TAG="dev"
    else
        docker build -t $IMAGE_NAME:latest .
        IMAGE_TAG="latest"
    fi
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
else
    log_warning "Skipping build step"
    IMAGE_TAG="latest"
fi

# Exit if build-only mode
if [ "$BUILD_ONLY" = true ]; then
    log_success "Build completed. Exiting (build-only mode)."
    exit 0
fi

# Stop and remove existing container
log_info "Stopping existing container..."
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Deploy based on environment
if [ "$ENVIRONMENT" = "development" ]; then
    log_info "Starting development environment..."
    docker-compose -f docker-compose.dev.yml up -d
else
    log_info "Starting production environment..."
    
    # Create necessary directories
    mkdir -p ./letsencrypt
    
    # Start with docker-compose
    docker-compose up -d
fi

# Wait for container to be ready
log_info "Waiting for container to be ready..."
sleep 10

# Health check
log_info "Performing health check..."
if curl -f http://localhost:$PORT/api/health > /dev/null 2>&1; then
    log_success "Health check passed"
else
    log_warning "Health check failed, but container might still be starting..."
fi

# Show container status
log_info "Container status:"
docker ps | grep $IMAGE_NAME || docker ps | grep tmc

# Show logs
log_info "Recent logs:"
if [ "$ENVIRONMENT" = "development" ]; then
    docker-compose -f docker-compose.dev.yml logs --tail=20 tmc-website-dev
else
    docker-compose logs --tail=20 tmc-website
fi

echo ""
log_success "Deployment completed!"
echo ""
echo "📋 Deployment Summary:"
echo "  Environment: $ENVIRONMENT"
echo "  Image: $IMAGE_NAME:$IMAGE_TAG"
echo "  Container: $CONTAINER_NAME"
echo "  Port: $PORT"
echo ""
echo "🔗 Access URLs:"
echo "  Local: http://localhost:$PORT"
if [ "$ENVIRONMENT" = "production" ]; then
    echo "  Production: https://$DOMAIN"
    echo "  Traefik Dashboard: http://localhost:8080"
fi
echo ""
echo "📊 Useful Commands:"
echo "  View logs: docker-compose logs -f"
echo "  Stop: docker-compose down"
echo "  Restart: docker-compose restart"
echo "  Shell access: docker exec -it $CONTAINER_NAME sh"
echo ""
log_success "Happy deploying! 🎉"
