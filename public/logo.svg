<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#14b8a6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="60" cy="60" r="55" fill="url(#logoGradient)" opacity="0.1"/>
  
  <!-- Main Logo Shape -->
  <g transform="translate(30, 30)">
    <!-- T -->
    <rect x="0" y="0" width="60" height="8" fill="url(#logoGradient)"/>
    <rect x="26" y="0" width="8" height="60" fill="url(#logoGradient)"/>
    
    <!-- Signal Waves -->
    <g transform="translate(45, 15)">
      <path d="M0 15 Q7.5 7.5 15 15" stroke="url(#logoGradient)" stroke-width="3" fill="none" opacity="0.8"/>
      <path d="M0 15 Q10 5 20 15" stroke="url(#logoGradient)" stroke-width="2" fill="none" opacity="0.6"/>
      <path d="M0 15 Q12.5 2.5 25 15" stroke="url(#logoGradient)" stroke-width="1.5" fill="none" opacity="0.4"/>
    </g>
    
    <!-- Connection Dots -->
    <circle cx="15" cy="45" r="3" fill="#2563eb"/>
    <circle cx="30" cy="45" r="3" fill="#14b8a6"/>
    <circle cx="45" cy="45" r="3" fill="#2563eb"/>
  </g>
</svg>
